{"name": "@napi-rs/nice-linux-x64-musl", "version": "1.0.4", "cpu": ["x64"], "main": "nice.linux-x64-musl.node", "files": ["nice.linux-x64-musl.node"], "description": "https://linux.die.net/man/2/nice binding for Node.js", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api", "nice"], "license": "MIT", "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"url": "git+ssh://**************/Brooooooklyn/nice.git", "type": "git"}, "os": ["linux"], "libc": ["musl"]}